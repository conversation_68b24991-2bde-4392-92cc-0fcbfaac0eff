<?php

namespace app\admin\model\mycurrency\lease;

use think\Model;
use traits\model\SoftDelete;

class Strategy extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'mycurrency_lease_strategy';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'types_text',
        'package_types_text',
        'status_text'
    ];
    

    
    public function getTypesList()
    {
        return ['1' => __('Types 1'), '2' => __('Types 2'), '3' => __('Types 3')];
    }

    public function getPackageTypesList()
    {
        return ['1' => __('Package_types 1'), '2' => __('Package_types 2'), '3' => __('Package_types 3')];
    }

    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2')];
    }


    public function getTypesTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['types']) ? $data['types'] : '');
        $list = $this->getTypesList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getPackageTypesTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['package_types']) ? $data['package_types'] : '');
        $list = $this->getPackageTypesList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }




}
