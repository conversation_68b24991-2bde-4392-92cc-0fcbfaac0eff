<?php

namespace app\admin\controller\mycurrency\operate\distribution;

use app\common\controller\Backend;
use app\common\model\mycurrency\OperateDistributionUser;
use app\common\model\mycurrency\OperateDistributionUserApplyfor;
use think\Db;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 营销-分销-分销员-分销员申请管理
 *
 * @icon fa fa-circle-o
 */
class Userapplyfor extends Backend
{

    /**
     * Userapplyfor模型对象
     * @var \app\admin\model\mycurrency\operate\distribution\Userapplyfor
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\mycurrency\operate\distribution\Userapplyfor;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->assignconfig('toexamine', $this->auth->check('mycurrency/operate/distribution/userapplyfor/toexamine')); //审核权限
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['user','admin'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','full_name','mobile','explain','status','admin_explain','createtime','updatetime']);
                $row->visible(['user']);
				$row->getRelation('user')->visible(['nickname','mobile']);
				$row->visible(['admin']);
				$row->getRelation('admin')->visible(['nickname']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 审核
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function toexamine($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params['admin_id'] = $this->auth->id;
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            if($result == true && $params['status'] == OperateDistributionUserApplyfor::STATUS_YITONGGUO){
                $distribution_user_add = [
                    'user_id' => $row->user_id,
                    'full_name' => $row->full_name,
                    'mobile' => $row->mobile,
                    'status' => OperateDistributionUser::STATUS_ZHENGCHANG,
                    'money' => 0,
                    'pid' => 0,
                    'ppid' => 0,
                    'poster_image' => '',
                    'createtime' => time(),
                    'avatar' => $row->avatar,
                ];
                $distribution_user = OperateDistributionUser::create($distribution_user_add);
                $poster_image = OperateDistributionUser::poster($distribution_user->id);
                OperateDistributionUser::where(['id' => $distribution_user->id])->update([
                    'poster_image' => $poster_image
                ]);
            }
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

}
