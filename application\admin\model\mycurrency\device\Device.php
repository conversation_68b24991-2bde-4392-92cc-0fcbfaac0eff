<?php

namespace app\admin\model\mycurrency\device;

use think\Model;
use traits\model\SoftDelete;

class Device extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'mycurrency_device';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text',
        'use_status_text',
        'is_fault_text',
        'is_online_text'
    ];
    

    
    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2')];
    }

    public function getUseStatusList()
    {
        return ['1' => __('Use_status 1'), '2' => __('Use_status 2')];
    }

    public function getIsFaultList()
    {
        return ['1' => __('Is_fault 1'), '2' => __('Is_fault 2')];
    }

    public function getIsOnlineList()
    {
        return ['1' => __('Is_online 1'), '2' => __('Is_online 2')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getUseStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['use_status']) ? $data['use_status'] : '');
        $list = $this->getUseStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getIsFaultTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_fault']) ? $data['is_fault'] : '');
        $list = $this->getIsFaultList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getIsOnlineTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_online']) ? $data['is_online'] : '');
        $list = $this->getIsOnlineList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function store()
    {
        return $this->belongsTo('app\admin\model\mycurrency\merchant\Store', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
