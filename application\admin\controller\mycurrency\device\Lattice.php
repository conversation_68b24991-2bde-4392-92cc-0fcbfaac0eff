<?php

namespace app\admin\controller\mycurrency\device;

use app\common\controller\Backend;

/**
 * 设备-格口管理
 *
 * @icon fa fa-circle-o
 */
class Lattice extends Backend
{

    /**
     * Lattice模型对象
     * @var \app\admin\model\mycurrency\device\Lattice
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\mycurrency\device\Lattice;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("useStatusList", $this->model->getUseStatusList());
        $this->view->assign("isFaultList", $this->model->getIsFaultList());

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index($ids = null)
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $device_id = $this->request->request('device_id');
            $other_where = [];
            if ($device_id) {
                $other_where['device_id'] = $device_id;
            }
            $list = $this->model
                    ->with(['device','goods'])
                    ->where($where)
                    ->where($other_where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','number','status','use_status','is_fault','createtime','updatetime','usetime']);
                $row->visible(['device']);
				$row->getRelation('device')->visible(['number']);
				$row->visible(['goods']);
				$row->getRelation('goods')->visible(['id']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        $this->assignconfig('device_id', $ids);
        return $this->view->fetch();
    }

}
