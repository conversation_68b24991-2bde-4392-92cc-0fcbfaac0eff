<?php

namespace app\admin\controller\mycurrency\merchant\staff;

use app\admin\model\mycurrency\merchant\staff\StaffGroup;
use app\common\controller\Backend;
use app\common\library\Auth;
use app\common\library\MerchantStaffAuth;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 工作人员管理
 *
 * @icon fa fa-user
 */
class Staff extends Backend
{

    protected $relationSearch = true;
    protected $searchFields = 'id,username,nickname';

    /**
     * @var \app\admin\model\User
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\mycurrency\merchant\staff\Staff();

    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->with('group')
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
            foreach ($list as $k => $v) {
                $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                $v->hidden(['password', 'salt']);
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            $this->view->assign('groupList', build_select('row[group_id]', StaffGroup::column('id,name'), 1, ['class' => 'form-control selectpicker']));
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $merchant_staff_auth = new MerchantStaffAuth();
        $ret = $merchant_staff_auth->register($params['username'],$params['password'],$params['email'],$params['mobile'],[
            'store_id' => $params['store_id'],
            'group_id' => $params['group_id'],
            'nickname' => $params['nickname'],
            'avatar' => $params['avatar'],
            'status' => $params['status'],
        ]);
        if ($ret) {
            $this->success('添加成功');
        } else {
            $this->error('添加失败');
        }
    }

//    /**
//     * 添加
//     */
//    public function add()
//    {
//        if ($this->request->isPost()) {
//            $this->token();
//        }
//        $this->view->assign('groupList', build_select('row[group_id]', StaffGroup::column('id,name'), 1, ['class' => 'form-control selectpicker']));
//        return parent::add();
//    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $this->view->assign('groupList', build_select('row[group_id]', StaffGroup::column('id,name'), $row['group_id'], ['class' => 'form-control selectpicker']));
        return parent::edit($ids);
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        Auth::instance()->delete($row['id']);
        $this->success();
    }

}
