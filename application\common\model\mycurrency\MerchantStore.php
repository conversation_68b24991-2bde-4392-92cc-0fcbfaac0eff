<?php

namespace app\common\model\mycurrency;

use addons\mycurrency\library\Common;
use app\common\model\Config;
use think\Exception;
use think\Model;
use traits\model\SoftDelete;

class MerchantStore extends Model
{

    //use SoftDelete;

    

    // 表名
    protected $name = 'mycurrency_merchant_store';

    // 追加属性
    protected $append = [
        'status_text',
        'dorrhead_image_str',
    ];

    const STATUS_ZHENGCHENG = 1;//状态 - 正常
    const STATUS_JINYONG = 2;//状态 - 禁用

    /**
     * 添加门店
     * @param array $params 注册信息
     * @param array $admin 管理员账号信息
     * @param int $p_agent_id 上级信息
    */
    public static function storeAdd($params,$admin,$p_agent_id = 0){
        if ($p_agent_id == 0){
            if($params['divideinto'] > 100){
                throw new Exception('分成比例不能大于100');
                return false;
            }
        }else{
            $p_agent = Agent::get($p_agent_id);
            if(!$p_agent){
                throw new Exception('上级不存在');
                return false;
            }
//            if($params['divideinto'] > $p_agent['divideinto']){
//                throw new Exception("分成比例不能高于{$p_agent['divideinto']}");
//                return false;
//            }
        }
        $params['agent_id'] = $p_agent_id;
        $params['createtime'] = time();
        $store = self::create($params);
        if(!$store){
            throw new Exception('添加失败');
            return false;
        }

        $identity = MerchantStaff::IDENTITY_MENDIAN;//身份
        $store_id = $store->id;//门店id
        $avatar = Config::where(['name' => 'user_default_headportrait'])->value('value');
        $ret = MerchantStaff::register($admin['username'],$admin['password'],'',$params['phone'],[
            'identity' => $identity,
            'store_id' => $store_id,
            'group_id' => 1,
            'nickname' => $params['store_name'],
            'avatar' => $avatar,
            'status' => 'normal',
        ]);
        if (!$ret){
            throw new Exception('管理员账号添加失败');
            return false;
        }
        return true;
    }

    /**
     * 编辑门店
     * @param array $params 编辑信息
     * @param int $store_id 被编辑信息id
    */
    public static function storeUpdate($params,$store_id){
        $store = self::get($store_id);
        if (!$store){
            throw new Exception('信息不存在');
            return false;
        }
        if($store['agent_id'] != 0){
            $p_agent = Agent::where(['id' => $store['agent_id'],'deletetime' => null])->find();
            if ($p_agent){
//                if($params['divideinto'] > $p_agent['divideinto']){
//                    throw new Exception("分成比例不能大于{$p_agent['divideinto']}");
//                    return false;
//                }
            }
        }else{
            if($params['divideinto'] > 100){
                throw new Exception('分成比例不能大于100');
                return false;
            }
        }

        $res = self::where(['id' => $store_id])->update($params);
        return true;
    }


    public function getDorrheadImageAttr($value){
        $baseUrl = Common::GetHost();
        return $baseUrl . $value;
    }
    // public function getAlbumImagesAttr($value){
    //     $all = explode(',',$value);

    //     $baseUrl = Common::GetHost();

    //         foreach($all as $k => $v){
    //             $all[$k] = $baseUrl . $v;
    //         }


    //     return $all;
    // }
    public function getDorrheadImageStrAttr($value, $data)
    {
        return $data['dorrhead_image'];
    }

//    public function getWechatCodeImageAttr($value){
//        if($value){
//            $baseUrl = Common::GetHost();
//            return $baseUrl . $value;
//        }
//        return '';
//    }
//
//    public function getAlipayCodeImageAttr($value){
//        if($value){
//            $baseUrl = Common::GetHost();
//            return $baseUrl . $value;
//        }
//        return '';
//    }

    public function getStatusTextAttr($value, $data)
    {
        $str = '';
        if ($data['status'] == self::STATUS_ZHENGCHENG){
            $str = "正常";
        }elseif ($data['status'] == self::STATUS_JINYONG){
            $str = "禁用";
        }
        return $str;
    }

    public function getCreateTimeAttr($value){
        return $value != null ? date('Y-m-d H:i:s', $value) : '';
    }

    //店铺评价记录
    public function evaluate()
    {
        return $this->hasMany(MerchantStoreEvaluate::class, 'store_id', 'id');
    }

    //门店收益记录
    public function profit()
    {
        return $this->hasMany(MerchantProfit::class, 'store_id', 'id')->sum('actual_income');
    }

}
