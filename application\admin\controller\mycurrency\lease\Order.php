<?php

namespace app\admin\controller\mycurrency\lease;

use app\common\controller\Backend;
use app\common\model\mycurrency\LeaseOrder;
use app\common\model\mycurrency\LeaseOrderDevice;
use think\Db;

/**
 * 租赁-租赁订单管理
 *
 * @icon fa fa-circle-o
 */
class Order extends Backend
{

    /**
     * Order模型对象
     * @var \app\admin\model\mycurrency\lease\Order
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\mycurrency\lease\Order;
        $this->view->assign("strategyTypesList", $this->model->getStrategyTypesList());
        $this->view->assign("useStatusList", $this->model->getUseStatusList());
        $this->view->assign("cancellTypeList", $this->model->getCancellTypeList());
        $this->view->assign("payStatusList", $this->model->getPayStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with([
                        'user',
                        'deposit',
                        'strategy',
                        'package',
                        'orderdevice'=>function($query){
                            //$query->with(['device']);
                        }
                    ])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                //门店名称
               $row->append(['store_name','device_name','device_lattice_number']);
               $row->store_name = Db::name('mycurrency_merchant_store')->where(['id'=>$row['store_id']])->value('store_name');

               //设备名称与格口号
                $order_device = Db::name('mycurrency_lease_order_device')->where(['lease_order_id'=>$row['id']])->find();
                $device = Db::name('mycurrency_device')->where(['id'=>$order_device['device_id']])->find();
                $lattice = Db::name('mycurrency_device_lattice')->where(['id'=>$order_device['device_lattice_id']])->find();
                $row->device_name = $device['title'];
                $row->device_lattice_number = $lattice['number'];

                //支付方式
                $row->payment_method = '未知支付方式';
                if (!empty($row['credit_id'] ?? null)) {
                    $row->payment_method = '免押支付';
                } elseif (!empty($row['deposit_id'] ?? null)) {
                    $deposit = Db::name('mycurrency_lease_deposit')->where(['id' => $row['deposit_id']])->find();
                    $pay_type = $deposit['pay_type'] ?? 0;
                    
                    if ($pay_type == 1 || $pay_type == 2) {
                        $row->payment_method = '预付金支付';
                    } elseif ($pay_type == 3) {
                        $row->payment_method = '余额支付';
                    }
                }

                if ($row['use_status'] == LeaseOrder::USE_STATUS_JINXINGZHONG){
                    if ($row['orderdevice']['status'] == LeaseOrderDevice::STATUS_ZULINZHONG){//租赁中
                        $row['use_status'] = 402;
                    }elseif ($row['orderdevice']['status'] == LeaseOrderDevice::STATUS_GUIHUANZHONG){//使用中
                        $row['use_status'] = 404;
                    }
                }
                $row->visible(['id','goods_title','sn','strategy_types','strategy_duration','strategy_unit_price','limit_start_time','limit_end_time','strategy_free_duration','strategy_free_frequency','strategy_free_cycle','strategy_overtime_money','strategy_overtime_company','lease_time','return_time','overtime_duration','rent_fee','overtime_fee','paymen_required','paid_dlready','use_status','cancell_type','pay_status','createtime','updatetime','payment_method']);
                $row->visible(['user']);
				$row->getRelation('user')->visible(['nickname','mobile']);
				$row->visible(['deposit']);
				$row->getRelation('deposit')->visible(['id']);
				$row->visible(['strategy']);
				$row->getRelation('strategy')->visible(['title']);
				$row->visible(['package']);
				$row->getRelation('package')->visible(['title']);
                $row->visible(['orderdevice']);
                $row->getRelation('orderdevice')->visible(['id','device_id','device_lattice_id','use_start_time','use_end_time','status','cancel_type']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

}
