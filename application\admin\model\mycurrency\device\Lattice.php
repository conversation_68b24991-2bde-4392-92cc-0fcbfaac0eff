<?php

namespace app\admin\model\mycurrency\device;

use think\Model;
use traits\model\SoftDelete;

class <PERSON><PERSON><PERSON> extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'mycurrency_device_lattice';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text',
        'use_status_text',
        'is_fault_text',
        'usetime_text'
    ];
    

    
    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2')];
    }

    public function getUseStatusList()
    {
        return ['1' => __('Use_status 1'), '2' => __('Use_status 2')];
    }

    public function getIsFaultList()
    {
        return ['1' => __('Is_fault 1'), '2' => __('Is_fault 2')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getUseStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['use_status']) ? $data['use_status'] : '');
        $list = $this->getUseStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getIsFaultTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_fault']) ? $data['is_fault'] : '');
        $list = $this->getIsFaultList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getUsetimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['usetime']) ? $data['usetime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setUsetimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function device()
    {
        return $this->belongsTo('app\admin\model\mycurrency\Device', 'device_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function goods()
    {
        return $this->belongsTo('app\admin\model\billiard\store\Goods', 'store_goods_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
