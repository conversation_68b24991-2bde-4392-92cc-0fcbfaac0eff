<?php

return [
    'User_id'              => '用户id',
    'Amount'               => '押金支付金额',
    'Pay_type'             => '支付类型',
    'Pay_type 1'           => '微信',
    'Pay_type 2'           => '支付宝',
    'Pay_type 3'           => '余额',
    'Status'               => '状态',
    'Status -1'            => '已取消',
    'Set status to -1'     => '设为已取消',
    'Status 1'             => '未支付',
    'Set status to 1'      => '设为未支付',
    'Status 2'             => '已支付',
    'Set status to 2'      => '设为已支付',
    'Status 3'             => '退款中',
    'Set status to 3'      => '设为退款中',
    'Status 4'             => '已退款',
    'Set status to 4'      => '设为已退款',
    'Refund_type'          => '退款方式',
    'Refund_type 1'        => '原路退回',
    'Refund_type 2'        => '线下退款【一些支付超过一年的有管理线下退款后更改状态】',
    'Refund_voucher'       => '线下退款凭据',
    'Place_time'           => '发起充值押金时间',
    'Payment_time'         => '支付完成时间',
    'Refund_time'          => '发起退款时间',
    'Refund_complete_time' => '退款完成时间',
    'Createtime'           => '创建时间',
    'Updatetime'           => '更新时间',
    'Deletetime'           => '删除时间',
    'User.nickname'        => '昵称',
    'User.mobile'          => '手机号',

    'Status 5'            => '已扣款',
    'Status -2'            => '支付超时',
];
