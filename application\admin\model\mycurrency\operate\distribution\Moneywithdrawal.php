<?php

namespace app\admin\model\mycurrency\operate\distribution;

use think\Model;
use traits\model\SoftDelete;

class Moneywithdrawal extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'mycurrency_operate_distribution_money_withdrawal';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'payment_mode_text',
        'status_text',
        'cancellation_type_text',
        'applytime_text',
        'rejecttime_text',
        'toexaminttime_text',
        'paymenttime_text'
    ];
    

    
    public function getPaymentModeList()
    {
        return ['1' => __('Payment_mode 1'), '2' => __('Payment_mode 2'), '3' => __('Payment_mode 3'), '4' => __('Payment_mode 4'), '5' => __('Payment_mode 5')];
    }

    public function getStatusList()
    {
        return ['-1' => __('Status -1'), '-2' => __('Status -2'), '1' => __('Status 1'), '2' => __('Status 2'), '3' => __('Status 3'), '4' => __('Status 4')];
    }

    public function getCancellationTypeList()
    {
        return ['1' => __('Cancellation_type 1'), '2' => __('Cancellation_type 2')];
    }


    public function getPaymentModeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['payment_mode']) ? $data['payment_mode'] : '');
        $list = $this->getPaymentModeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getCancellationTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['cancellation_type']) ? $data['cancellation_type'] : '');
        $list = $this->getCancellationTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getApplytimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['applytime']) ? $data['applytime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getRejecttimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['rejecttime']) ? $data['rejecttime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getToexaminttimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['toexaminttime']) ? $data['toexaminttime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getPaymenttimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['paymenttime']) ? $data['paymenttime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setApplytimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setRejecttimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setToexaminttimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setPaymenttimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function user()
    {
        return $this->belongsTo('User', 'distribution_user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
