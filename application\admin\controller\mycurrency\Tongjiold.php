<?php

namespace app\admin\controller\mycurrency;

use app\common\controller\Backend;
use think\Db;

/**
 * 测试管理
 *
 * @icon fa fa-circle-o
 */
class Tongjiold extends Backend
{
    protected $noNeedRight = '*';
    /**
     * Tongji模型对象
     * @var \app\admin\model\mycurrency\Tongji
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\mycurrency\Tongji;
        $this->view->assign("weekList", $this->model->getWeekList());
        $this->view->assign("flagList", $this->model->getFlagList());
        $this->view->assign("genderdataList", $this->model->getGenderdataList());
        $this->view->assign("hobbydataList", $this->model->getHobbydataList());
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("stateList", $this->model->getStateList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','user_id','title']);
                
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }


    public function index1(){
        return $this->view->fetch();
    }
    
    /**
     * 获取营业收入统计数据
     */
    public function getRevenue()
    {
        $result = [];
        
        // 获取请求参数
        $startTime = $this->request->param('start_time/d', 0); // 开始时间戳
        $endTime = $this->request->param('end_time/d', 0);     // 结束时间戳
        $filterType = $this->request->param('filter_type/s', 'day'); // 过滤类型：day, week, month
        
        // 如果未指定时间范围，默认为过去一段时间
        if (!$endTime) {
            $endTime = time();
        }
        
        if (!$startTime) {
            // 根据过滤类型设置默认时间范围
            switch ($filterType) {
                case 'week':
                    $startTime = strtotime('-7 days', $endTime);
                    break;
                case 'month':
                    $startTime = strtotime('-30 days', $endTime);
                    break;
                default: // day 或其他
                    $startTime = strtotime('-1 day', $endTime);
                    break;
            }
        }
        
        // 获取总营业收入（所有时间）
        $totalRevenue = Db::name('mycurrency_lease_order')
            ->where('use_status', 6) // 已完成的订单
            ->sum('paymen_required') ?? 0;
        $result['total_revenue'] = number_format($totalRevenue, 2);
        
        // 获取指定时间范围内的营业收入
        $periodRevenue = Db::name('mycurrency_lease_order')
            ->where('use_status', 6)
            ->where('return_time', 'between', [$startTime, $endTime])
            ->sum('paymen_required') ?? 0;
        $result['period_revenue'] = number_format($periodRevenue, 2);
        
        // 获取指定时间范围内的实收金额
        $periodOrderPayment = Db::name('mycurrency_lease_order')
            ->where('complete_time', 'between', [$startTime, $endTime])
            ->sum('paid_dlready') ?? 0;
        $result['period_order_payment'] = number_format($periodOrderPayment, 2);
        
        // 获取订单总笔数（所有时间）
        $totalPaymentCount = Db::name('mycurrency_lease_order')
            ->where('return_time', '>', 0) // 租赁结束时间有值
            ->count() ?? 0;
        $result['payment_count'] = $totalPaymentCount;
        
        // 获取指定时间范围内的支付笔数
        $periodPaymentCount = Db::name('mycurrency_lease_order')
            ->where('complete_time', 'between', [$startTime, $endTime])
            ->count() ?? 0;
        $result['period_payment_count'] = $periodPaymentCount;
        
        // 获取未付款/欠款总金额（所有时间）
        $totalPendingPayment = Db::name('mycurrency_lease_order')
            ->where('pending_payment', '>', 0)
            ->sum('pending_payment') ?? 0;
        $result['total_pending_payment'] = number_format($totalPendingPayment, 2);
        
        // 获取指定时间范围内新增欠款金额
        $periodPendingPayment = Db::name('mycurrency_lease_order')
            ->where('createtime', 'between', [$startTime, $endTime])
            ->where('pending_payment', '>', 0)
            ->sum('pending_payment') ?? 0;
        $result['period_pending_payment'] = number_format($periodPendingPayment, 2);
        
        // 获取历史欠款订单总笔数（所有时间）
        $totalPendingOrders = Db::name('mycurrency_lease_order')
            ->where('pending_payment', '>', 0)
            ->count() ?? 0;
        $result['total_pending_orders'] = $totalPendingOrders;
        
        // 获取指定时间范围内新增欠款订单笔数
        $periodPendingOrders = Db::name('mycurrency_lease_order')
            ->where('createtime', 'between', [$startTime, $endTime])
            ->where('pending_payment', '>', 0)
            ->count() ?? 0;
        $result['period_pending_orders'] = $periodPendingOrders;
        
        // 根据过滤类型获取详细图表数据
        $chartData = $this->getChartData($startTime, $endTime, $filterType);
        $result['chart_data'] = $chartData;
        
        return json($result);
    }
    
    /**
     * 获取图表数据
     * @param int $startTime 开始时间戳
     * @param int $endTime 结束时间戳
     * @param string $filterType 过滤类型：day, week, month
     * @return array 图表数据
     */
    protected function getChartData($startTime, $endTime, $filterType)
    {
        $result = [
            'categories' => [],      // X轴分类（日期）
            'total_revenue' => [],   // 总收入数据
            'paid_amount' => [],     // 实收金额数据
            'pending_amount' => [],  // 欠款金额数据
            'order_count' => [],     // 订单数量
            'pending_order_count' => [] // 欠款订单数量
        ];
        
        // 根据过滤类型确定时间粒度和格式
        switch ($filterType) {
            case 'month':
                $format = 'Y-m-d';
                $interval = 86400; // 按天统计，间隔一天
                $groupFormat = '%Y-%m-%d';
                break;
            case 'week':
                $format = 'Y-m-d';
                $interval = 86400; // 按天统计，间隔一天
                $groupFormat = '%Y-%m-%d';
                break;
            default: // day
                $format = 'H:i';
                $interval = 3600; // 按小时统计，间隔一小时
                $groupFormat = '%Y-%m-%d %H:00:00';
                break;
        }
        
        // 生成时间区间点
        $timePoints = [];
        $current = $startTime;
        while ($current <= $endTime) {
            $timePoints[] = $current;
            $result['categories'][] = date($format, $current);
            $current += $interval;
        }
        
        // 提前填充默认值为0
        $count = count($timePoints);
        $result['total_revenue'] = array_fill(0, $count, 0);
        $result['paid_amount'] = array_fill(0, $count, 0);
        $result['pending_amount'] = array_fill(0, $count, 0);
        $result['order_count'] = array_fill(0, $count, 0);
        $result['pending_order_count'] = array_fill(0, $count, 0);
        
        // 查询总收入数据（按时间分组）
        $totalRevenueData = Db::name('mycurrency_lease_order')
            ->where('use_status', 6)
            ->where('return_time', 'between', [$startTime, $endTime])
            ->field("FROM_UNIXTIME(return_time, '{$groupFormat}') as time_point, SUM(paymen_required) as total")
            ->group('time_point')
            ->select();
            
        // 查询实收金额数据（按时间分组）
        $paidAmountData = Db::name('mycurrency_lease_order')
            ->where('complete_time', 'between', [$startTime, $endTime])
            ->field("FROM_UNIXTIME(complete_time, '{$groupFormat}') as time_point, SUM(paid_dlready) as total")
            ->group('time_point')
            ->select();
        
        // 查询欠款金额数据（按时间分组）
        $pendingAmountData = Db::name('mycurrency_lease_order')
            ->where('createtime', 'between', [$startTime, $endTime])
            ->where('pending_payment', '>', 0)
            ->field("FROM_UNIXTIME(createtime, '{$groupFormat}') as time_point, SUM(pending_payment) as total")
            ->group('time_point')
            ->select();
        
        // 查询订单数量数据（按时间分组）
        $orderCountData = Db::name('mycurrency_lease_order')
            ->where('return_time', 'between', [$startTime, $endTime])
            ->field("FROM_UNIXTIME(return_time, '{$groupFormat}') as time_point, COUNT(*) as total")
            ->group('time_point')
            ->select();
        
        // 查询欠款订单数量数据（按时间分组）
        $pendingOrderCountData = Db::name('mycurrency_lease_order')
            ->where('createtime', 'between', [$startTime, $endTime])
            ->where('pending_payment', '>', 0)
            ->field("FROM_UNIXTIME(createtime, '{$groupFormat}') as time_point, COUNT(*) as total")
            ->group('time_point')
            ->select();
        
        // 填充数据到结果数组
        $this->fillChartData($result, $totalRevenueData, 'total_revenue', $format, $filterType);
        $this->fillChartData($result, $paidAmountData, 'paid_amount', $format, $filterType);
        $this->fillChartData($result, $pendingAmountData, 'pending_amount', $format, $filterType);
        $this->fillChartData($result, $orderCountData, 'order_count', $format, $filterType);
        $this->fillChartData($result, $pendingOrderCountData, 'pending_order_count', $format, $filterType);
        
        return $result;
    }
    
    /**
     * 填充图表数据
     * @param array &$result 结果数组
     * @param array $data 数据源
     * @param string $key 结果数组中的键名
     * @param string $format 日期格式
     * @param string $filterType 过滤类型
     */
    protected function fillChartData(&$result, $data, $key, $format, $filterType)
    {
        if (empty($data)) {
            return;
        }
        
        $dateMap = [];
        foreach ($data as $item) {
            $timePoint = $item['time_point'];
            
            // 根据过滤类型格式化日期作为索引
            if ($filterType == 'day') {
                // 小时格式
                $date = date($format, strtotime($timePoint));
            } else {
                // 日期格式
                $date = date($format, strtotime($timePoint));
            }
            
            if (isset($dateMap[$date])) {
                $dateMap[$date] += floatval($item['total']);
            } else {
                $dateMap[$date] = floatval($item['total']);
            }
        }
        
        // 填充数据到结果数组
        foreach ($result['categories'] as $index => $category) {
            if (isset($dateMap[$category])) {
                $result[$key][$index] = $dateMap[$category];
            }
        }
    }


    //数据大屏
    public function index2(){
        return $this->view->fetch();
    }
    
    /**
     * 获取省份列表
     */
    public function getProvinceList()
    {
        $provinceList = Db::name('area')
            ->where('level', 1)
            ->field('id as value, name as label')
            ->select();

        return json(['code' => 1, 'msg' => '获取成功', 'data' => $provinceList ?? []]);
    }
    
    /**
     * 获取城市列表
     * @param int $province_id 省份ID
     */
    public function getCityList()
    {
        $province_id = $this->request->param('province_id/d', 0);
        
        if (!$province_id) {
            return json(['code' => 0, 'msg' => '参数错误', 'data' => []]);
        }
        
        $cityList = Db::name('area')
            ->where('level', 2)
            ->where('pid', $province_id)
            ->field('id as value, name as label')
            ->select();
        
        return json(['code' => 1, 'msg' => '获取成功', 'data' => $cityList ?? []]);
    }
    
    /**
     * 获取区县列表
     * @param int $city_id 城市ID
     */
    public function getDistrictList()
    {
        $city_id = $this->request->param('city_id/d', 0);
        
        if (!$city_id) {
            return json(['code' => 0, 'msg' => '参数错误', 'data' => []]);
        }
        
        $districtList = Db::name('area')
            ->where('level', 3)
            ->where('pid', $city_id)
            ->field('id as value, name as label')
            ->select();
        
        return json(['code' => 1, 'msg' => '获取成功', 'data' => $districtList ?? []]);
    }

    /**
     * 获取代理商列表
     * @param int $province_id 省份ID (可选)
     * @param int $city_id 城市ID (可选)
     * @param int $area_id 区县ID (可选)
     */
    public function getAgentList()
    {
        $province_id = $this->request->param('province_id/d', 0);
        $city_id = $this->request->param('city_id/d', 0);
        $area_id = $this->request->param('area_id/d', 0);
        
        $where = [];
        $where['deletetime'] = null; // 未删除的
        $where['status'] = 1; // 正常状态
        
        // 构建查询条件，如果有相应参数则添加到查询条件中
        if ($area_id) {
            // 优先使用区县ID筛选
            $where['area_id'] = $area_id;
        } elseif ($city_id) {
            // 如果没有区县ID但有城市ID
            $where['city_id'] = $city_id;
        } elseif ($province_id) {
            // 如果只有省份ID
            $where['province_id'] = $province_id;
        }
        
        $agentList = Db::name('mycurrency_agent')
            ->where($where)
            ->field('id as value, name as label, level')
            ->select();
//        echo db()->getLastSql();die;
        
        return json(['code' => 1, 'msg' => '获取成功', 'data' => $agentList ?? []]);
    }
    
    /**
     * 获取门店列表
     * @param int $agent_id 代理商ID (可选)
     * @param int $province_id 省份ID (可选)
     * @param int $city_id 城市ID (可选)
     * @param int $area_id 区县ID (可选)
     */
    public function getStoreList()
    {
        $agent_id = $this->request->param('agent_id/d', 0);
        $province_id = $this->request->param('province_id/d', 0);
        $city_id = $this->request->param('city_id/d', 0);
        $area_id = $this->request->param('area_id/d', 0);
        
        // 查询条件
        $where = [];
        $where['deletetime'] = null; // 未删除的
        $where['status'] = 1; // 正常状态
        
        if ($agent_id) {
            // 如果传入了代理商ID
            // 获取该代理商及其所有下级代理商ID
            $agent_ids = $this->getAgentAndSubordinates($agent_id);
            $where['agent_id'] = ['in', $agent_ids];
        } else {
            // 如果没有传入代理商ID，则使用地区筛选
            if ($area_id) {
                // 优先使用区县ID筛选
                $where['area_id'] = $area_id;
            } elseif ($city_id) {
                // 如果没有区县ID但有城市ID
                $where['city_id'] = $city_id;
            } elseif ($province_id) {
                // 如果只有省份ID
                $where['province_id'] = $province_id;
            }
        }
        
        // 查询门店列表
        $storeList = Db::name('mycurrency_merchant_store')
            ->where($where)
            ->field('id as value, store_name as label')
            ->select();
        
        return json(['code' => 1, 'msg' => '获取成功', 'data' => $storeList ?? []]);
    }
    
    /**
     * 获取门店ID集合
     * @param int $agent_id 代理商ID (可选)
     * @param int $province_id 省份ID (可选)
     * @param int $city_id 城市ID (可选)
     * @param int $area_id 区县ID (可选)
     */
    public function getStoreIds()
    {
        $agent_id = $this->request->param('agent_id/d', 0);
        $province_id = $this->request->param('province_id/d', 0);
        $city_id = $this->request->param('city_id/d', 0);
        $area_id = $this->request->param('area_id/d', 0);
        $store_id = $this->request->param('store_id/d', 0);
        
        // 如果直接提供了门店ID，则直接返回
        if ($store_id) {
            return json(['code' => 1, 'msg' => '获取成功', 'data' => [$store_id]]);
        }
        
        // 查询条件
        $where = [];
        $where['deletetime'] = null; // 未删除的
        $where['status'] = 1; // 正常状态
        
        if ($agent_id) {
            // 如果传入了代理商ID
            // 获取该代理商及其所有下级代理商ID
            $agent_ids = $this->getAgentAndSubordinates($agent_id);
            $where['agent_id'] = ['in', $agent_ids];
        } else {
            // 如果没有传入代理商ID，则使用地区筛选
            if ($area_id) {
                // 优先使用区县ID筛选
                $where['area_id'] = $area_id;
            } elseif ($city_id) {
                // 如果没有区县ID但有城市ID
                $where['city_id'] = $city_id;
            } elseif ($province_id) {
                // 如果只有省份ID
                $where['province_id'] = $province_id;
            }
        }
        
        // 查询门店ID集合
        $storeIds = Db::name('mycurrency_merchant_store')
            ->where($where)
            ->column('id');
        
        return json(['code' => 1, 'msg' => '获取成功', 'data' => $storeIds ?? []]);
    }
    
    /**
     * 获取代理商及其所有下级代理商ID
     * @param int $agent_id 代理商ID
     * @return array 代理商ID数组
     */
    protected function getAgentAndSubordinates($agent_id)
    {
        if (!$agent_id) {
            return [];
        }
        
        // 初始化结果数组，包含自身
        $result = [$agent_id];
        
        // 查询直接下级
        $subordinates = $this->getSubordinates($agent_id);
        
        // 如果有下级，则递归查询下级的下级
        if (!empty($subordinates)) {
            foreach ($subordinates as $sub_id) {
                $result = array_merge($result, $this->getAgentAndSubordinates($sub_id));
            }
        }
        
        // 去重并返回
        return array_unique($result);
    }
    
    /**
     * 获取直接下级代理商ID
     * @param int $agent_id 代理商ID
     * @return array 下级代理商ID数组
     */
    protected function getSubordinates($agent_id)
    {
        return Db::name('mycurrency_agent')
            ->where('pid', $agent_id)
            ->where('deletetime', null)
            ->where('status', 1)
            ->column('id');
    }

    /**
     * 获取设备统计信息（柜子和球杆）
     * @param array|string $store_ids 门店ID集合或单个门店ID
     */
    public function getDeviceStats()
    {
        // 添加调试日志
        \think\Log::write('getDeviceStats 接收参数: ' . json_encode($this->request->param()), 'info');
        
        $store_ids = $this->request->param('store_ids/a', []);
        $store_id = $this->request->param('store_id/d', 0);
        
        // 如果直接提供了单个门店ID，优先使用它
        if ($store_id > 0) {
            \think\Log::write('使用单个门店ID: ' . $store_id, 'info');
            $store_ids = [$store_id];
        }
        
        // 如果没有传入store_ids参数或者参数为空，则尝试使用别的参数进行查询
        if (empty($store_ids)) {
            $agent_id = $this->request->param('agent_id/d', 0);
            $province_id = $this->request->param('province_id/d', 0);
            $city_id = $this->request->param('city_id/d', 0);
            $area_id = $this->request->param('area_id/d', 0);
            
            \think\Log::write('store_ids为空，使用其他参数: agent_id=' . $agent_id . ', province_id=' . $province_id . ', city_id=' . $city_id . ', area_id=' . $area_id, 'info');
            
            // 构建查询条件
            $where = [];
            $where['deletetime'] = null; // 未删除的
            $where['status'] = 1; // 正常状态
            
            if ($agent_id) {
                // 如果传入了代理商ID
                \think\Log::write('根据代理商ID查询: ' . $agent_id, 'info');
                // 获取该代理商及其所有下级代理商ID
                $agent_ids = $this->getAgentAndSubordinates($agent_id);
                $where['agent_id'] = ['in', $agent_ids];
                \think\Log::write('包含下级代理商，总计: ' . count($agent_ids) . '个', 'info');
            } else {
                // 如果没有传入代理商ID，则使用地区筛选
                if ($area_id) {
                    // 优先使用区县ID筛选
                    \think\Log::write('使用区县ID筛选: ' . $area_id, 'info');
                    $where['area_id'] = $area_id;
                } elseif ($city_id) {
                    // 如果没有区县ID但有城市ID
                    \think\Log::write('使用城市ID筛选: ' . $city_id, 'info');
                    $where['city_id'] = $city_id;
                } elseif ($province_id) {
                    // 如果只有省份ID
                    \think\Log::write('使用省份ID筛选: ' . $province_id, 'info');
                    $where['province_id'] = $province_id;
                } else {
                    \think\Log::write('未提供任何筛选条件，使用默认条件', 'info');
                }
            }
            
            // 查询门店ID集合
            $store_ids = Db::name('mycurrency_merchant_store')
                ->where($where)
                ->column('id');
                
            \think\Log::write('查询SQL: ' . Db::name('mycurrency_merchant_store')->getLastSql(), 'info');
            \think\Log::write('查询到的门店数量: ' . count($store_ids), 'info');
        }
        
        // 初始化结果数组
        $result = [
            'code' => 1,
            'msg' => '获取成功',
            'data' => [
                'device' => [
                    'total' => 0,
                    'online' => 0,
                    'offline' => 0
                ],
                'stick' => [
                    'total' => 0,
                    'available' => 0,
                    'in_use' => 0
                ]
            ]
        ];
        
        // 如果门店ID为空，直接返回默认结果
        if (empty($store_ids)) {
            \think\Log::write('门店ID集合为空，返回默认结果', 'info');
            return json($result);
        }
        
        \think\Log::write('开始统计设备数据，门店ID: ' . json_encode($store_ids), 'info');
        
        // 统计柜子信息
        $deviceStats = $this->getDeviceStatsData($store_ids);
        $result['data']['device'] = $deviceStats;
        
        // 统计球杆信息
        $stickStats = $this->getStickStatsData($store_ids);
        $result['data']['stick'] = $stickStats;
        
        \think\Log::write('统计结果: ' . json_encode($result['data']), 'info');
        
        return json($result);
    }
    
    /**
     * 获取柜子统计数据
     * @param array $store_ids 门店ID集合
     * @return array 柜子统计数据
     */
    protected function getDeviceStatsData($store_ids)
    {
        // 记录开始查询
        \think\Log::write('开始查询柜子统计数据，门店ID数量: ' . count($store_ids), 'info');
        
        // 初始化结果
        $stats = [
            'total' => 0,
            'online' => 0,
            'offline' => 0
        ];
        
        if (empty($store_ids)) {
            return $stats;
        }
        
        // 查询条件：门店ID在指定集合中且未删除
        $where = [
            'store_id' => ['in', $store_ids],
            'deletetime' => null,
            'status' => 1 // 只统计正常状态的柜子
        ];
        
        // 获取总数
        $total = Db::name('mycurrency_device')
            ->where($where)
            ->count();
        $stats['total'] = isset($total) ? $total : 0;
        
        \think\Log::write('柜子总数查询SQL: ' . Db::name('mycurrency_device')->getLastSql(), 'info');
        
        // 获取在线数
        $onlineWhere = array_merge($where, ['is_online' => 2]); // 2=在线
        $online = Db::name('mycurrency_device')
            ->where($onlineWhere)
            ->count();
        $stats['online'] = isset($online) ? $online : 0;
        
        // 获取离线数
        $offlineWhere = array_merge($where, ['is_online' => 1]); // 1=不在线
        $offline = Db::name('mycurrency_device')
            ->where($offlineWhere)
            ->count();
        $stats['offline'] = isset($offline) ? $offline : 0;
        
        \think\Log::write('柜子统计结果: ' . json_encode($stats), 'info');
        
        return $stats;
    }
    
    /**
     * 获取球杆统计数据
     * @param array $store_ids 门店ID集合
     * @return array 球杆统计数据
     */
    protected function getStickStatsData($store_ids)
    {
        // 记录开始查询
        \think\Log::write('开始查询球杆统计数据，门店ID数量: ' . count($store_ids), 'info');
        
        // 初始化结果
        $stats = [
            'total' => 0,       // 投放总数
            'available' => 0,    // 可用球杆数量
            'in_use' => 0       // 使用中球杆数量
        ];
        
        if (empty($store_ids)) {
            return $stats;
        }
        
        // 首先获取这些门店下的所有设备ID
        $deviceIds = Db::name('mycurrency_device')
            ->where([
                'store_id' => ['in', $store_ids],
                'deletetime' => null,
                'status' => 1
            ])
            ->column('id');
            
        \think\Log::write('设备ID查询SQL: ' . Db::name('mycurrency_device')->getLastSql(), 'info');
        \think\Log::write('查询到的设备ID数量: ' . count($deviceIds), 'info');
        
        if (empty($deviceIds)) {
            return $stats;
        }
        
        // 基础查询条件：设备ID在指定集合中、未删除、状态正常、无故障、已绑定产品
        $baseWhere = [
            'device_id' => ['in', $deviceIds],
            'deletetime' => null,
            'status' => 1, // 正常状态
            'is_fault' => 1, // 无故障
            'store_goods_id' => ['>', 0] // 已绑定产品
        ];
        
        // 获取投放总数（排除禁用的故障的，没有绑定产品的）
        $total = Db::name('mycurrency_device_lattice')
            ->where($baseWhere)
            ->count();
        $stats['total'] = isset($total) ? $total : 0;
        
        \think\Log::write('球杆总数查询SQL: ' . Db::name('mycurrency_device_lattice')->getLastSql(), 'info');
        
        // 获取使用中球杆数（投放中且无杆的）
        $inUseWhere = array_merge($baseWhere, ['use_status' => 2]); // 无杆状态
        $inUse = Db::name('mycurrency_device_lattice')
            ->where($inUseWhere)
            ->count();
        $stats['in_use'] = isset($inUse) ? $inUse : 0;
        
        // 获取可用球杆数（投放中且有杆的）
        $availableWhere = array_merge($baseWhere, ['use_status' => 1]); // 有杆状态
        $available = Db::name('mycurrency_device_lattice')
            ->where($availableWhere)
            ->count();
        $stats['available'] = isset($available) ? $available : 0;
        
        \think\Log::write('球杆统计结果: ' . json_encode($stats), 'info');
        
        return $stats;
    }

    /**
     * 获取平台统计数据
     */
    public function getPlatformStats()
    {
        try {
            // 初始化返回数据结构
            $result = [
                'total_revenue' => '0.00',      // 累计营业额
                'total_duration' => '0天0:00',  // 租用总时长
                'total_orders' => 0,            // 租用总订单数量
                'total_agents' => 0,            // 总代理数量
                'total_stores' => 0             // 总商户数量
            ];
            
            // 获取累计营业额
            $totalRevenue = Db::name('mycurrency_lease_order')
                ->where('use_status', 6) // 已完成的订单
                ->sum('paymen_required') ?? 0;
            $result['total_revenue'] = number_format($totalRevenue, 2);
            
            // 获取租用总时长（秒）
            $totalDurationSeconds = Db::name('mycurrency_lease_order')
                ->where('use_status', 6) // 已完成的订单
                ->sum('lease_duration') ?? 0;
            
            // 将秒转换为"天时:分"格式
            $days = floor($totalDurationSeconds / 86400);
            $hours = floor(($totalDurationSeconds % 86400) / 3600);
            $minutes = floor(($totalDurationSeconds % 3600) / 60);
            $result['total_duration'] = $days . '天' . $hours . ':' . str_pad($minutes, 2, '0', STR_PAD_LEFT);
            
            // 获取租用总订单数
            $result['total_orders'] = Db::name('mycurrency_lease_order')
                ->where('use_status', 6) // 已完成的订单
                ->count() ?? 0;
            
            // 获取总代理数
            $result['total_agents'] = Db::name('mycurrency_agent')
                ->where('status', 1) // 活跃的代理
                ->count() ?? 0;
            
            // 获取总商户数
            $result['total_stores'] = Db::name('mycurrency_merchant_store')
                ->where('status', 1) // 活跃的商户
                ->count() ?? 0;
            
            // 记录访问日志
            $this->saveLog("获取平台统计数据", json_encode($result, JSON_UNESCAPED_UNICODE));
            
            // 返回成功结果
            return json(['code' => 1, 'msg' => '获取成功', 'data' => $result]);
        } catch (\Exception $e) {
            // 记录错误日志
            $this->saveLog("获取平台统计数据失败", $e->getMessage());
            
            // 返回错误信息
            return json(['code' => 0, 'msg' => '获取平台统计数据失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 保存日志
     * @param string $action 操作
     * @param string $content 内容
     */
    protected function saveLog($action, $content)
    {
        try {
            // 记录日志
            Db::name('mycurrency_log')->insert([
                'admin_id' => $this->auth->id ?? 0,
                'username' => $this->auth->username ?? 'system',
                'action' => $action,
                'content' => $content,
                'createtime' => time(),
                'ip' => $this->request->ip()
            ]);
        } catch (\Exception $e) {
            // 记录日志失败，不影响主流程
        }
    }

    /**
     * 获取订单统计数据（按日、月、年筛选）
     * @return \think\response\Json
     */
    public function getOrderStats()
    {
        $result = [
            'code' => 1,
            'msg' => '获取成功',
            'data' => [
                'categories' => [],
                'series' => []
            ]
        ];
        
        // 获取请求参数
        $filterType = $this->request->param('filter_type/s', 'day'); // 过滤类型：day, month, year
        $startTime = $this->request->param('start_time/d', 0); // 开始时间戳
        $endTime = $this->request->param('end_time/d', 0);     // 结束时间戳
        $storeIds = $this->request->param('store_ids/a', []); // 门店IDs
        $storeId = $this->request->param('store_id/d', 0);  // 单个门店ID
        $agentId = $this->request->param('agent_id/d', 0);  // 代理商ID
        $provinceId = $this->request->param('province_id/d', 0); // 省份ID
        $cityId = $this->request->param('city_id/d', 0);    // 城市ID
        $areaId = $this->request->param('area_id/d', 0);    // 区县ID
        
        // 如果有单个门店ID，优先使用它
        if ($storeId > 0) {
            $storeIds = [$storeId];
        }
        
        // 如果没有门店IDs，则根据代理商ID或地区ID获取
        if (empty($storeIds) && ($agentId || $provinceId || $cityId || $areaId)) {
            // 构建查询条件
            $where = [];
            $where['deletetime'] = null; // 未删除的
            $where['status'] = 1; // 正常状态
            
            if ($agentId) {
                // 如果传入了代理商ID
                // 获取该代理商及其所有下级代理商ID
                $agentIds = $this->getAgentAndSubordinates($agentId);
                $where['agent_id'] = ['in', $agentIds];
            } else {
                // 如果没有传入代理商ID，则使用地区筛选
                if ($areaId) {
                    // 优先使用区县ID筛选
                    $where['area_id'] = $areaId;
                } elseif ($cityId) {
                    // 如果没有区县ID但有城市ID
                    $where['city_id'] = $cityId;
                } elseif ($provinceId) {
                    // 如果只有省份ID
                    $where['province_id'] = $provinceId;

                }
            }

            
            // 查询门店ID集合
            $storeIds = Db::name('mycurrency_merchant_store')
                ->where($where)
                ->column('id');
        }
        
        // 新增：如果通过筛选条件后门店IDs为空且有设置筛选条件，直接返回空结果
        if (empty($storeIds) && ($agentId || $provinceId || $cityId || $areaId || $storeId > 0)) {
            // 根据过滤类型生成空的类别数据，保持格式一致
            switch ($filterType) {
                case 'year':
                    // 生成12个月的空数据
                    for ($i = 1; $i <= 12; $i++) {
                        $result['data']['categories'][] = sprintf('%02d月', $i);
                        $result['data']['series'][] = 0;
                    }
                    break;
                case 'month':
                    // 生成当月天数的空数据
                    $daysInMonth = date('t');
                    for ($i = 1; $i <= $daysInMonth; $i++) {
                        $result['data']['categories'][] = sprintf('%02d日', $i);
                        $result['data']['series'][] = 0;
                    }
                    break;
                default: // day
                    // 生成24小时的空数据
                    for ($i = 0; $i < 24; $i++) {
                        $result['data']['categories'][] = sprintf('%02d:00', $i);
                        $result['data']['series'][] = 0;
                    }
                    break;
            }
            
            return json($result);
        }

        // 如果未指定时间范围，默认为当前时间段
        if (!$endTime) {
            $endTime = time();
        }
        
        if (!$startTime) {
            // 根据过滤类型设置默认时间范围
            switch ($filterType) {
                case 'year':
                    // 当年开始
                    $startTime = strtotime(date('Y-01-01 00:00:00', $endTime));
                    break;
                case 'month':
                    // 当月开始
                    $startTime = strtotime(date('Y-m-01 00:00:00', $endTime));
                    break;
                default: // day
                    // 当天开始
                    $startTime = strtotime(date('Y-m-d 00:00:00', $endTime));
                    break;
            }
        }
        
        // 根据过滤类型确定时间粒度和格式
        switch ($filterType) {
            case 'year':
                $format = 'Y-m'; // 按月显示
                $interval = 86400 * 30; // 大约一个月的间隔
                $groupFormat = '%Y-%m'; // 按月分组
                break;
            case 'month':
                $format = 'd'; // 按天显示
                $interval = 86400; // 一天的间隔
                $groupFormat = '%Y-%m-%d'; // 按天分组
                break;
            default: // day
                $format = 'H:i'; // 按小时显示
                $interval = 3600; // 一小时的间隔
                $groupFormat = '%Y-%m-%d %H:00:00'; // 按小时分组
                break;
        }
        
        // 生成时间区间点
        $timePoints = [];
        $current = $startTime;
        while ($current <= $endTime) {
            $timePoints[] = $current;
            
            // 根据过滤类型格式化日期
            if ($filterType == 'year') {
                $result['data']['categories'][] = date('m月', $current);
            } elseif ($filterType == 'month') {
                $result['data']['categories'][] = date('d日', $current);
            } else { // day
                $result['data']['categories'][] = date('H:i', $current);
            }
            
            $current += $interval;
        }
        
        // 提前填充默认值为0
        $count = count($timePoints);
        $result['data']['series'] = array_fill(0, $count, 0);
        
        // 构建查询条件
        $query = Db::name('mycurrency_lease_order')
            ->where('complete_time', 'between', [$startTime, $endTime]);
        
        // 如果有门店IDs，则添加到查询条件
        if (!empty($storeIds)) {
            $query->where('store_id', 'in', $storeIds);
        }
        
        // 查询订单数量数据（按时间分组）
        $orderCountData = $query->field("FROM_UNIXTIME(complete_time, '{$groupFormat}') as time_point, COUNT(*) as total, SUM(paid_dlready) as amount")
            ->group('time_point')
            ->select();

        // 填充数据到结果数组
        if (!empty($orderCountData)) {
            $dateMap = [];
            foreach ($orderCountData as $item) {
                $timePoint = $item['time_point'];
                
                // 根据过滤类型格式化日期作为索引
                if ($filterType == 'year') {
                    // 年筛选，按月格式化
                    $date = date('m月', strtotime($timePoint));
                } elseif ($filterType == 'month') {
                    // 月筛选，按天格式化
                    $date = date('d日', strtotime($timePoint));
                } else { // day
                    // 日筛选，按小时格式化
                    $date = date('H:i', strtotime($timePoint));
                }
                
                if (isset($dateMap[$date])) {
                    $dateMap[$date] += intval($item['total']);
                } else {
                    $dateMap[$date] = intval($item['total']);
                }
            }
            
            // 填充数据到结果数组
            foreach ($result['data']['categories'] as $index => $category) {
                if (isset($dateMap[$category])) {
                    $result['data']['series'][$index] = $dateMap[$category];
                }
            }
        }
        
        return json($result);
    }
    
    /**
     * 获取营业额统计数据（按日、月、年筛选）
     * @return \think\response\Json
     */
    public function getRevenueStats()
    {
        $result = [
            'code' => 1,
            'msg' => '获取成功',
            'data' => [
                'categories' => [],
                'series' => []
            ]
        ];
        
        // 获取请求参数
        $filterType = $this->request->param('filter_type/s', 'day'); // 过滤类型：day, month, year
        $startTime = $this->request->param('start_time/d', 0); // 开始时间戳
        $endTime = $this->request->param('end_time/d', 0);     // 结束时间戳
        $storeIds = $this->request->param('store_ids/a', []); // 门店IDs
        $storeId = $this->request->param('store_id/d', 0);  // 单个门店ID
        $agentId = $this->request->param('agent_id/d', 0);  // 代理商ID
        $provinceId = $this->request->param('province_id/d', 0); // 省份ID
        $cityId = $this->request->param('city_id/d', 0);    // 城市ID
        $areaId = $this->request->param('area_id/d', 0);    // 区县ID
        
        // 如果有单个门店ID，优先使用它
        if ($storeId > 0) {
            $storeIds = [$storeId];
        }
        
        // 如果没有门店IDs，则根据代理商ID或地区ID获取
        if (empty($storeIds) && ($agentId || $provinceId || $cityId || $areaId)) {
            // 构建查询条件
            $where = [];
            $where['deletetime'] = null; // 未删除的
            $where['status'] = 1; // 正常状态
            
            if ($agentId) {
                // 如果传入了代理商ID
                // 获取该代理商及其所有下级代理商ID
                $agentIds = $this->getAgentAndSubordinates($agentId);
                $where['agent_id'] = ['in', $agentIds];
            } else {
                // 如果没有传入代理商ID，则使用地区筛选
                if ($areaId) {
                    // 优先使用区县ID筛选
                    $where['area_id'] = $areaId;
                } elseif ($cityId) {
                    // 如果没有区县ID但有城市ID
                    $where['city_id'] = $cityId;
                } elseif ($provinceId) {
                    // 如果只有省份ID
                    $where['province_id'] = $provinceId;
                }
            }
            
            // 查询门店ID集合
            $storeIds = Db::name('mycurrency_merchant_store')
                ->where($where)
                ->column('id');
        }
        
        // 新增：如果通过筛选条件后门店IDs为空且有设置筛选条件，直接返回空结果
        if (empty($storeIds) && ($agentId || $provinceId || $cityId || $areaId || $storeId > 0)) {
            // 根据过滤类型生成空的类别数据，保持格式一致
            switch ($filterType) {
                case 'year':
                    // 生成12个月的空数据
                    for ($i = 1; $i <= 12; $i++) {
                        $result['data']['categories'][] = sprintf('%02d月', $i);
                        $result['data']['series'][] = 0;
                    }
                    break;
                case 'month':
                    // 生成当月天数的空数据
                    $daysInMonth = date('t');
                    for ($i = 1; $i <= $daysInMonth; $i++) {
                        $result['data']['categories'][] = sprintf('%02d日', $i);
                        $result['data']['series'][] = 0;
                    }
                    break;
                default: // day
                    // 生成24小时的空数据
                    for ($i = 0; $i < 24; $i++) {
                        $result['data']['categories'][] = sprintf('%02d:00', $i);
                        $result['data']['series'][] = 0;
                    }
                    break;
            }
            
            return json($result);
        }
        
        // 如果未指定时间范围，默认为当前时间段
        if (!$endTime) {
            $endTime = time();
        }
        
        if (!$startTime) {
            // 根据过滤类型设置默认时间范围
            switch ($filterType) {
                case 'year':
                    // 当年开始
                    $startTime = strtotime(date('Y-01-01 00:00:00', $endTime));
                    break;
                case 'month':
                    // 当月开始
                    $startTime = strtotime(date('Y-m-01 00:00:00', $endTime));
                    break;
                default: // day
                    // 当天开始
                    $startTime = strtotime(date('Y-m-d 00:00:00', $endTime));
                    break;
            }
        }
        
        // 根据过滤类型确定时间粒度和格式
        switch ($filterType) {
            case 'year':
                $format = 'Y-m'; // 按月显示
                $interval = 86400 * 30; // 大约一个月的间隔
                $groupFormat = '%Y-%m'; // 按月分组
                break;
            case 'month':
                $format = 'd'; // 按天显示
                $interval = 86400; // 一天的间隔
                $groupFormat = '%Y-%m-%d'; // 按天分组
                break;
            default: // day
                $format = 'H:i'; // 按小时显示
                $interval = 3600; // 一小时的间隔
                $groupFormat = '%Y-%m-%d %H:00:00'; // 按小时分组
                break;
        }
        
        // 生成时间区间点
        $timePoints = [];
        $current = $startTime;
        while ($current <= $endTime) {
            $timePoints[] = $current;
            
            // 根据过滤类型格式化日期
            if ($filterType == 'year') {
                $result['data']['categories'][] = date('m月', $current);
            } elseif ($filterType == 'month') {
                $result['data']['categories'][] = date('d日', $current);
            } else { // day
                $result['data']['categories'][] = date('H:i', $current);
            }
            
            $current += $interval;
        }
        
        // 提前填充默认值为0
        $count = count($timePoints);
        $result['data']['series'] = array_fill(0, $count, 0);
        
        // 构建查询条件
        $query = Db::name('mycurrency_lease_order')
            ->where('return_time', 'between', [$startTime, $endTime])
            ->where('use_status', 6); // 已完成的订单
        
        // 如果有门店IDs，则添加到查询条件
        if (!empty($storeIds)) {
            $query->where('store_id', 'in', $storeIds);
        }
        
        // 查询营业额数据（按时间分组）
        $revenueData = $query->field("FROM_UNIXTIME(return_time, '{$groupFormat}') as time_point, SUM(paymen_required) as total")
            ->group('time_point')
            ->select();
        
        // 填充数据到结果数组
        if (!empty($revenueData)) {
            $dateMap = [];
            foreach ($revenueData as $item) {
                $timePoint = $item['time_point'];
                
                // 根据过滤类型格式化日期作为索引
                if ($filterType == 'year') {
                    // 年筛选，按月格式化
                    $date = date('m月', strtotime($timePoint));
                } elseif ($filterType == 'month') {
                    // 月筛选，按天格式化
                    $date = date('d日', strtotime($timePoint));
                } else { // day
                    // 日筛选，按小时格式化
                    $date = date('H:i', strtotime($timePoint));
                }
                
                if (isset($dateMap[$date])) {
                    $dateMap[$date] += floatval($item['total']);
                } else {
                    $dateMap[$date] = floatval($item['total']);
                }
            }
            
            // 填充数据到结果数组
            foreach ($result['data']['categories'] as $index => $category) {
                if (isset($dateMap[$category])) {
                    $result['data']['series'][$index] = $dateMap[$category];
                }
            }
        }
        
        return json($result);
    }
    
    /**
     * 获取区域订单排名数据
     * @return \think\response\Json
     */
    public function getAreaOrderRanking()
    {
        $result = [
            'code' => 1,
            'msg' => '获取成功',
            'data' => []
        ];
        
        // 获取请求参数
        $areaType = $this->request->param('area_type/d', 1); // 区域类型：1=省，2=市，3=区
        $orderType = $this->request->param('order_type/s', 'order'); // 排序类型：order=订单量，amount=金额
        $limit = $this->request->param('limit/d', 10); // 返回数量限制，默认前10名
        
        // 构建基础查询
        $baseQuery = Db::name('mycurrency_lease_order')
            ->alias('o')
            ->join('mycurrency_merchant_store s', 'o.store_id = s.id')
            ->where('o.return_time', '>', 0); // 只统计有效订单（租赁结束时间不为空）
        
        // 获取筛选条件
        $provinceId = $this->request->param('province_id/d', 0);
        $cityId = $this->request->param('city_id/d', 0);
        $areaId = $this->request->param('area_id/d', 0);
        
        // 如果提供了地区筛选条件，检查是否有符合条件的门店
        if ($provinceId > 0 || $cityId > 0 || $areaId > 0) {
            $storeWhere = [
                'deletetime' => null,
                'status' => 1
            ];
            
            if ($areaId > 0) {
                $storeWhere['area_id'] = $areaId;
            } elseif ($cityId > 0) {
                $storeWhere['city_id'] = $cityId;
            } elseif ($provinceId > 0) {
                $storeWhere['province_id'] = $provinceId;
            }
            
            // 查询符合条件的门店
            $storeIds = Db::name('mycurrency_merchant_store')
                ->where($storeWhere)
                ->column('id');
                
            if (empty($storeIds)) {
                // 新增：如果没有门店，直接返回空结果
                return json($result);
            }
            
            // 添加门店筛选条件
            $baseQuery = $baseQuery->where('o.store_id', 'in', $storeIds);
        }
        
        // 根据区域类型构建查询条件
        switch ($areaType) {
            case 1: // 省级
                $query = $baseQuery->field([
                    's.province_id as area_id',
                    'count(o.id) as order_count',
                    'sum(o.paymen_required) as total_amount'
                ])
                ->join('area a', 's.province_id = a.id')
                ->where('s.province_id', '>', 0)
                ->group('s.province_id')
                ->field('a.name as area_name');
                break;
                
            case 2: // 市级
                $query = $baseQuery->field([
                    's.city_id as area_id',
                    'count(o.id) as order_count',
                    'sum(o.paymen_required) as total_amount'
                ])
                ->join('area a', 's.city_id = a.id')
                ->where('s.city_id', '>', 0)
                ->group('s.city_id')
                ->field('a.name as area_name');
                break;
                
            case 3: // 区县级
                $query = $baseQuery->field([
                    's.area_id as area_id',
                    'count(o.id) as order_count',
                    'sum(o.paymen_required) as total_amount'
                ])
                ->join('area a', 's.area_id = a.id')
                ->where('s.area_id', '>', 0)
                ->group('s.area_id')
                ->field('a.name as area_name');
                break;
                
            default:
                return json(['code' => 0, 'msg' => '参数错误', 'data' => []]);
        }
        
        // 根据排序类型确定排序字段
        $orderField = $orderType == 'amount' ? 'total_amount' : 'order_count';
        
        // 执行查询，获取排名数据
        $rankingData = $query->order($orderField, 'desc')
            ->limit($limit)
            ->select();
            
        // 处理数据格式，添加排名字段
        if (!empty($rankingData)) {
            foreach ($rankingData as $key => $item) {
                $rankingData[$key]['rank'] = $key + 1;
                
                // 计算进度条百分比（相对于第一名的比例，最大100%）
                if ($key == 0) {
                    $rankingData[$key]['percentage'] = 100;
                    $maxValue = $item[$orderField];
                } else {
                    $rankingData[$key]['percentage'] = $maxValue > 0 ? 
                        round(($item[$orderField] / $maxValue) * 100) : 0;
                }
                
                // 确保所有值都有格式化
                $rankingData[$key]['order_count'] = intval($item['order_count']);
                $rankingData[$key]['total_amount'] = number_format($item['total_amount'], 2);
                
                // 展示值（根据排序类型确定）
                $rankingData[$key]['value'] = $orderType == 'amount' ? 
                    $rankingData[$key]['total_amount'] : $rankingData[$key]['order_count'];
            }
        }
        
        $result['data'] = $rankingData;
        
        return json($result);
    }
    
    /**
     * 获取代理商订单排名数据
     * @return \think\response\Json
     */
    public function getAgentOrderRanking()
    {
        $result = [
            'code' => 1,
            'msg' => '获取成功',
            'data' => []
        ];
        
        // 获取请求参数
        $agentType = $this->request->param('agent_type/d', 1); // 代理商类型：1=省代，2=市代，3=区代，4=业务员
        $orderType = $this->request->param('order_type/s', 'order'); // 排序类型：order=订单量，amount=金额
        $limit = $this->request->param('limit/d', 10); // 返回数量限制，默认前10名
        
        // 获取筛选条件
        $provinceId = $this->request->param('province_id/d', 0);
        $cityId = $this->request->param('city_id/d', 0);
        $areaId = $this->request->param('area_id/d', 0);
        
        // 构建代理商查询条件
        $agentWhere = [
            'status' => 1, // 正常状态
            'deletetime' => null // 未删除
        ];
        
        // 如果提供了地区筛选条件，添加到代理商查询中
        if ($provinceId > 0) {
            $agentWhere['province_id'] = $provinceId;
        }
        if ($cityId > 0) {
            $agentWhere['city_id'] = $cityId;
        }
        if ($areaId > 0) {
            $agentWhere['area_id'] = $areaId;
        }
        
        // 根据代理商类型设置不同查询条件
        if ($agentType == 4) {
            // 业务员
            $agentWhere['role_type'] = 2; // 业务员角色
        } else {
            // 省代、市代、区代
            $agentWhere['role_type'] = 1; // 代理商角色
            $agentWhere['level'] = $agentType; // 代理商级别：1=省代,2=市代,3=区代
        }
        
        // 获取符合条件的代理商列表
        $agents = Db::name('mycurrency_agent')
                ->where($agentWhere)
                ->field('id, name, level, role_type')
                ->select();

        if (empty($agents)) {
            return json(['code' => 1, 'msg' => '暂无代理商数据', 'data' => []]);
        }
        
        // 对每个代理商查询订单数据
        $rankingData = [];
        $hasData = false; // 标记是否找到了有效数据
        
        foreach ($agents as $agent) {
            // 查询该代理商下的所有门店ID
            $storeIds = Db::name('mycurrency_merchant_store')
                    ->where([
                        'agent_id' => $agent['id'],
                        'status' => 1,
                        'deletetime' => null
                    ])
                    ->column('id');

            if (empty($storeIds)) {
                continue; // 如果没有门店，则跳过当前代理商
            }
            
            // 查询这些门店的订单数据
            $orderStats = Db::name('mycurrency_lease_order')
                    ->where([
                        'store_id' => ['in', $storeIds],
                        'return_time' => ['>', 0] // 只统计有效订单
                    ])
                    ->field([
                        'count(id) as order_count',
                        'sum(paid_dlready) as total_amount' // 使用paid_dlready作为金额统计字段
                    ])
                    ->find();
            
            // 如果没有订单数据，设置默认值
            $orderCount = isset($orderStats['order_count']) ? intval($orderStats['order_count']) : 0;
            $totalAmount = isset($orderStats['total_amount']) ? floatval($orderStats['total_amount']) : 0;
            
            // 只有有订单的代理商才加入排名
            if ($orderCount > 0 || $totalAmount > 0) {
                $hasData = true; // 标记找到了有效数据
                $rankingData[] = [
                    'agent_id' => $agent['id'],
                    'agent_name' => $agent['name'],
                    'agent_level' => $agent['level'],
                    'agent_role_type' => $agent['role_type'],
                    'order_count' => $orderCount,
                    'total_amount' => $totalAmount
                ];
            }
        }
        
        // 如果没有找到有效数据，直接返回空结果
        if (!$hasData) {
            return json(['code' => 1, 'msg' => '暂无订单数据', 'data' => []]);
        }
        
        // 根据排序类型对数据进行排序
        if ($orderType == 'amount') {
            // 按金额排序
            usort($rankingData, function($a, $b) {
                return $b['total_amount'] <=> $a['total_amount'];
            });
        } else {
            // 按订单数量排序
            usort($rankingData, function($a, $b) {
                return $b['order_count'] <=> $a['order_count'];
            });
        }
        
        // 限制返回数量
        $rankingData = array_slice($rankingData, 0, $limit);
        
        // 处理数据格式，添加排名字段
        if (!empty($rankingData)) {
            $orderField = $orderType == 'amount' ? 'total_amount' : 'order_count';
            $maxValue = isset($rankingData[0][$orderField]) ? $rankingData[0][$orderField] : 0;
            
            foreach ($rankingData as $key => $item) {
                $rankingData[$key]['rank'] = $key + 1;
                
                // 计算进度条百分比
                $rankingData[$key]['percentage'] = $maxValue > 0 ? 
                    round(($item[$orderField] / $maxValue) * 100) : 0;
                
                // 格式化输出值
                $rankingData[$key]['total_amount'] = number_format($item['total_amount'], 2);
                
                // 设置显示值（根据排序类型确定）
                $rankingData[$key]['name'] = $item['agent_name']; // 设置name字段，用于前端显示
                $rankingData[$key]['value'] = $orderType == 'amount' ? 
                    $rankingData[$key]['total_amount'] : $rankingData[$key]['order_count'];
            }
        }
        
        $result['data'] = $rankingData;
        
        return json($result);
    }

    /**
     * 获取商家订单排名数据
     * @return \think\response\Json
     */
    public function getStoreOrderRanking()
    {
        $result = [
            'code' => 1,
            'msg' => '获取成功',
            'data' => []
        ];
        
        // 获取请求参数
        $orderType = $this->request->param('order_type/s', 'order'); // 排序类型：order=订单量，amount=金额
        $limit = $this->request->param('limit/d', 10); // 返回数量限制，默认前10名
        
        // 根据筛选条件构建查询参数
        $where = [];
        
        // 可选的筛选条件
        $provinceId = $this->request->param('province_id/d', 0);
        $cityId = $this->request->param('city_id/d', 0);
        $areaId = $this->request->param('area_id/d', 0);
        $agentId = $this->request->param('agent_id/d', 0);
        
        // 初始化门店IDs变量，用于判断是否有符合条件的门店
        $storeIds = [];
        
        // 如果有代理商ID，获取该代理商下的所有门店
        if ($agentId > 0) {
            // 获取代理商及其下级代理商IDs
            $agentIds = $this->getAgentAndSubordinates($agentId);
            
            // 获取这些代理商下的门店IDs
            $storeIds = Db::name('mycurrency_merchant_store')
                ->where('agent_id', 'in', $agentIds)
                ->where('deletetime', null)
                ->where('status', 1)
                ->column('id');
                
            if (!empty($storeIds)) {
                $where['o.store_id'] = ['in', $storeIds];
            } else {
                // 新增：如果没有门店，直接返回空结果
                return json($result);
            }
        } else {
            // 构建商店查询条件
            $storeWhere = [
                'deletetime' => null,
                'status' => 1
            ];
            
            // 如果有地区筛选条件
            if ($areaId > 0) {
                $storeWhere['area_id'] = $areaId;
            } elseif ($cityId > 0) {
                $storeWhere['city_id'] = $cityId;
            } elseif ($provinceId > 0) {
                $storeWhere['province_id'] = $provinceId;
            }
            
            // 如果有地区筛选，查询是否有符合条件的门店
            if ($areaId > 0 || $cityId > 0 || $provinceId > 0) {
                $storeIds = Db::name('mycurrency_merchant_store')
                    ->where($storeWhere)
                    ->column('id');
                
                if (empty($storeIds)) {
                    // 新增：如果没有门店，直接返回空结果
                    return json($result);
                }
                
                // 如果有门店，添加到查询条件
                $where['o.store_id'] = ['in', $storeIds];
            }
            
            // 如果没有设置任何筛选条件，使用地区筛选
            if ($areaId > 0) {
                // 优先使用区县ID筛选
                $where['s.area_id'] = $areaId;
            } elseif ($cityId > 0) {
                // 如果没有区县ID但有城市ID
                $where['s.city_id'] = $cityId;
            } elseif ($provinceId > 0) {
                // 如果只有省份ID
                $where['s.province_id'] = $provinceId;
            }
        }
        
        // 只查询有效订单（租赁结束时间大于0）
        $where['o.return_time'] = ['>', 0];
        
        // 查询并统计数据
        $query = Db::name('mycurrency_lease_order')
            ->alias('o')
            ->join('mycurrency_merchant_store s', 'o.store_id = s.id')
            ->where($where)
            ->where('s.deletetime', null)
            ->where('s.status', 1);
            
        // 根据排序类型选择不同的统计字段和排序
        if ($orderType == 'amount') {
            // 按金额排序
            $rankingData = $query->field([
                's.id as store_id',
                's.store_name',
                'count(o.id) as order_count',
                'sum(o.paid_dlready) as total_amount'
            ])
            ->group('o.store_id')
            ->order('total_amount', 'desc')
            ->limit($limit)
            ->select();
        } else {
            // 默认按订单数量排序
            $rankingData = $query->field([
                's.id as store_id',
                's.store_name',
                'count(o.id) as order_count',
                'sum(o.paid_dlready) as total_amount'
            ])
            ->group('o.store_id')
            ->order('order_count', 'desc')
            ->limit($limit)
            ->select();
        }
        
        // 处理数据格式，添加排名字段
        if (!empty($rankingData)) {
            $orderField = $orderType == 'amount' ? 'total_amount' : 'order_count';
            $maxValue = 0;
            
            // 找出最大值
            foreach ($rankingData as $item) {
                $currentValue = floatval($item[$orderField]);
                if ($currentValue > $maxValue) {
                    $maxValue = $currentValue;
                }
            }
            
            // 格式化数据
            foreach ($rankingData as $key => $item) {
                $rankingData[$key]['rank'] = $key + 1;
                
                // 计算进度条百分比
                $rankingData[$key]['percentage'] = $maxValue > 0 ? 
                    round((floatval($item[$orderField]) / $maxValue) * 100) : 0;
                
                // 确保所有值都有格式化
                $rankingData[$key]['order_count'] = intval($item['order_count']);
                $rankingData[$key]['total_amount'] = number_format(floatval($item['total_amount']), 2);
                
                // 展示值（根据排序类型确定）
                $rankingData[$key]['name'] = $item['store_name']; // 设置name字段，用于前端显示
                $rankingData[$key]['value'] = $orderType == 'amount' ? 
                    $rankingData[$key]['total_amount'] : $rankingData[$key]['order_count'];
            }
        }
        
        $result['data'] = $rankingData;
        
        return json($result);
    }

    /**
     * 获取门店地图数据
     * @return \think\response\Json
     */
    public function getStoreMapData()
    {
        // 获取筛选参数
        $province_id = $this->request->param('province_id/d', 0);
        $city_id = $this->request->param('city_id/d', 0);
        $area_id = $this->request->param('area_id/d', 0);
        $agent_id = $this->request->param('agent_id/d', 0);
        $store_id = $this->request->param('store_id/d', 0);
        
        // 构建查询条件
        $where = [];
        $where['deletetime'] = null; // 未删除的
        $where['status'] = 1; // 正常状态
        
        // 如果提供了门店ID，直接查询该门店
        if ($store_id > 0) {
            $where['id'] = $store_id;
        } else {
            // 否则根据其他筛选条件查询
            if ($agent_id) {
                // 如果传入了代理商ID，获取该代理商及其所有下级代理商ID
                $agent_ids = $this->getAgentAndSubordinates($agent_id);
                $where['agent_id'] = ['in', $agent_ids];
            } else {
                // 如果没有传入代理商ID，则使用地区筛选
                if ($area_id) {
                    // 优先使用区县ID筛选
                    $where['area_id'] = $area_id;
                } elseif ($city_id) {
                    // 如果没有区县ID但有城市ID
                    $where['city_id'] = $city_id;
                } elseif ($province_id) {
                    // 如果只有省份ID
                    $where['province_id'] = $province_id;
                }
            }
        }
        
        // 查询门店数据
        $stores = Db::name('mycurrency_merchant_store')
            ->where($where)
            ->field('id, store_name, province_id, city_id, area_id, address, longitude, latitude, fullname, phone, trade_time, dorrhead_image, agent_id')
            ->select();
        
        // 如果有门店数据，查询设备相关数据并关联到门店
        $validStores = [];
        if (!empty($stores)) {
            foreach ($stores as &$store) {
                // 确保经纬度是有效的数值
                if (isset($store['longitude']) && isset($store['latitude'])) {
                    // 转换为浮点数
                    $store['longitude'] = floatval($store['longitude']);
                    $store['latitude'] = floatval($store['latitude']);
                    
                    // 检查经纬度是否有效
                    if (!empty($store['longitude']) && !empty($store['latitude']) && 
                        $store['longitude'] > 0 && $store['latitude'] > 0) {
                        // 获取门店设备统计数据
                        $deviceStats = $this->getStoreDeviceStats($store['id']);
                        $store['device_stats'] = $deviceStats;
                        
                        // 添加到有效门店列表
                        $validStores[] = $store;
                    }
                }
            }
        }
        
        // 记录处理结果
        $this->saveLog('getStoreMapData', '筛选条件: 省ID='.$province_id.', 市ID='.$city_id.', 区ID='.$area_id.', 代理ID='.$agent_id.', 门店ID='.$store_id.', 查询结果: '.count($stores).'条, 有效经纬度: '.count($validStores).'条');
        
        return json(['code' => 1, 'msg' => '获取成功', 'data' => $validStores ?? []]);
    }
    
    /**
     * 获取门店设备统计数据
     * @param int $store_id 门店ID
     * @return array 设备统计数据
     */
    protected function getStoreDeviceStats($store_id)
    {
        // 获取门店的柜子数量和状态
        $deviceStats = [
            'device_total' => 0,
            'device_online' => 0,
            'device_offline' => 0,
            'stick_total' => 0,
            'stick_available' => 0,
            'stick_in_use' => 0
        ];
        
        // 查询柜子数据
        $devices = Db::name('mycurrency_device')
            ->where('store_id', $store_id)
            ->where('deletetime', null)
            ->where('status', 1)
            ->select();
        
        $deviceStats['device_total'] = count($devices) ?? 0;
        
        $deviceIds = [];
        foreach ($devices as $device) {
            if ($device['is_online'] == 2) {
                $deviceStats['device_online']++;
            } else {
                $deviceStats['device_offline']++;
            }
            $deviceIds[] = $device['id'];
        }
        
        // 查询球杆数据
        if (!empty($deviceIds)) {
            // 总球杆数
            $deviceStats['stick_total'] = Db::name('mycurrency_device_lattice')
                ->where('device_id', 'in', $deviceIds)
                ->where('deletetime', null)
                ->where('status', 1)
                ->where('is_fault', 1)
                ->where('store_goods_id', '>', 0)
                ->count() ?? 0;
            
            // 可用球杆数
            $deviceStats['stick_available'] = Db::name('mycurrency_device_lattice')
                ->where('device_id', 'in', $deviceIds)
                ->where('deletetime', null)
                ->where('status', 1)
                ->where('is_fault', 1)
                ->where('store_goods_id', '>', 0)
                ->where('use_status', 1)
                ->count() ?? 0;
            
            // 使用中球杆数
            $deviceStats['stick_in_use'] = Db::name('mycurrency_device_lattice')
                ->where('device_id', 'in', $deviceIds)
                ->where('deletetime', null)
                ->where('status', 1)
                ->where('is_fault', 1)
                ->where('store_goods_id', '>', 0)
                ->where('use_status', 2)
                ->count() ?? 0;
        }
        
        return $deviceStats;
    }
}
