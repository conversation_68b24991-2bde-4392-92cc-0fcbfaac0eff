<?php

namespace app\merchant_staff\controller\billiard;

use app\common\controller\MerchantStaff;
use app\common\library\Ems;
use app\common\library\Sms;
use app\common\model\Area;
use app\common\model\mycurrency\MerchantStore;
use fast\Random;
use think\Config;
use think\Db;
use think\Exception;
use think\Validate;
use app\common\model\mycurrency\MerchantStaff as StaffMoney;
use app\common\model\mycurrency\Agent;

/**
 * 代理or业务员接口
 */
class Store extends MerchantStaff
{
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();

        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'));
        }
        if ($this->identity != 1){
            $this->error('非代理员或业务员身份');
        }
    }

    /**
     * 门店列表
    */
    public function storeList(){
        $params = $this->request->param();
        $rule = [
            ['page', 'require', '页码不能为空'],
            ['pageSize', 'require', '每页查询条数不能为空'],
        ];
        $check = $this->validate($params, $rule);
        if ($check !== true) {
            $this->error($check);
        }
        $agent_ids = [];
        if($this->agent['role_type'] == Agent::ROLE_TYPE_DAILI){
            $agent_ids = Agent::where(['pid' => $this->agent_id,'role_type' => Agent::ROLE_TYPE_YEWUYUAN,'deletetime' => null])->column('id');
        }
        $agent_ids[] = $this->agent_id;
        $where = [
            'agent_id' => ['in',$agent_ids],
            'deletetime' => null,
        ];
        $list = MerchantStore::where($where)
            ->limit(($params['page'] - 1) * $params['pageSize'], $params['pageSize'])
            ->order('id desc')
            ->field('*')
            ->select();
        $num = MerchantStore::where($where)->count();
        if ($num > $params['page'] * $params['pageSize']) {
            $nextpage = true;
        } else {
            $nextpage = false;
        }
        $this->success('获取成功', ['list' => $list, 'nextpage' => $nextpage]);
    }

    /**
     * 门店详情
     */
    public function storeInfo(){
        $params = $this->request->param();
        $rule = [
            ['store_id', 'require', '门店id不能为空'],
        ];
        $check = $this->validate($params, $rule);
        if ($check !== true) {
            $this->error($check);
        }
        $data = MerchantStore::get($params['store_id']);

        if(!$data){
            $this->error('门店不存在');
        }
        $this->success('加载成功',$data);
    }

    /**
     * 门店添加
     */
    public function storeAdd(){
        $params = $this->request->param();
        $rule = [
            ['store_name', 'require', '门店名称不能为空'],
            ['dorrhead_image', 'require', '门店图片不能为空'],
//            ['album_images', 'require', '门店图片不能为空'],
            ['province_id', 'require', '门店所属省id不能为空'],
            ['city_id', 'require', '门店所属市id不能为空'],
            ['area_id', 'require', '门店所属区县id不能为空'],
            ['address',"require", '门店详细地址不能为空'],
            ['longitude',"require", '经度不能为空'],
            ['latitude',"require", '纬度不能为空'],
            ['fullname', 'require', '联系人姓名不能为空'],
            ['phone', 'require', '联系人电话不能为空'],
            ['divideinto',"require", '分成比例不能为空'],
            ['status',"require", '状态不能为空'],
            ['trade_time',"require", '营业时间不能为空'],


            ['username',"require", '管理员登录账号不能为空'],
            ['password',"require", '管理员登录密码不能为空'],
        ];
        $check = $this->validate($params, $rule);
        if ($check !== true) {
            $this->error($check);
        }
        $admin = [
            'username' => $params['username'],
            'password' => $params['password'],
        ];
        unset($params['username']);
        unset($params['password']);
        Db::startTrans();
        try {
            $result = MerchantStore::storeAdd($params,$admin,$this->agent_id);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('添加成功');
    }

    /**
     * 门店编辑
     */
    public function storeUpdate(){
        $params = $this->request->param();
        $rule = [
            ['store_name', 'require', '门店名称不能为空'],
            ['dorrhead_image', 'require', '门店图片不能为空'],
//            ['album_images', 'require', '门店相册不能为空'],
            ['province_id', 'require', '门店所属省id不能为空'],
            ['city_id', 'require', '门店所属市id不能为空'],
            ['area_id', 'require', '门店所属区县id不能为空'],
            ['address',"require", '门店详细地址不能为空'],
            ['longitude',"require", '经度不能为空'],
            ['latitude',"require", '纬度不能为空'],
            ['fullname', 'require', '联系人姓名不能为空'],
            ['phone', 'require', '联系人电话不能为空'],
            ['divideinto',"require", '分成比例不能为空'],
            ['status',"require", '状态不能为空'],
            ['trade_time',"require", '营业时间不能为空'],

            ['x_store_id',"require", '门店id不能为空'],
        ];
        $check = $this->validate($params, $rule);
        if ($check !== true) {
            $this->error($check);
        }
        $x_store_id = $params['x_store_id'];
        unset($params['x_store_id']);
        $store = MerchantStore::get($x_store_id);
        if (!$store){
            $this->error('门店不存在');
        }
        Db::startTrans();
        try {
            $result = MerchantStore::storeUpdate($params,$x_store_id);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('编辑成功');
    }

    /**
     * 门店删除
     */
    public function storeDel(){
        $params = $this->request->param();
        $rule = [
            ['x_store_id',"require", '门店id不能为空'],
        ];
        $check = $this->validate($params, $rule);
        if ($check !== true) {
            $this->error($check);
        }
        $x_store_id = $params['x_store_id'];
        $store = MerchantStore::get($x_store_id);
        if (!$store){
            $this->error('门店不存在');
        }
        $store->deletetime = time();
        $store->save();
        $this->success('删除成功');
    }



}
